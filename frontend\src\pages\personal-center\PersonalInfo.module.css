/* 个人信息卡片样式 */
.personalInfoCard {
  margin-bottom: 24px;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.personalInfoCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* 装饰性背景元素 */
.decorativeCircle1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

.decorativeCircle2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  z-index: 1;
  animation: float 8s ease-in-out infinite reverse;
}

/* 主要内容区域 */
.contentArea {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  margin: 2px;
  border-radius: 14px;
  padding: 24px;
  transition: all 0.3s ease;
}

/* 标题栏 */
.titleBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}

/* 设置按钮 */
.settingsButton {
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  transition: all 0.3s ease;
}

.settingsButton:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  transform: scale(1.1);
}

/* 头像区域 */
.avatarContainer {
  position: relative;
}

.avatar {
  background: linear-gradient(135deg, #667eea, #764ba2);
  font-size: 28px;
  font-weight: 600;
  border: 4px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
}

/* 在线状态指示器 */
.onlineIndicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #52c41a;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  animation: pulse 2s infinite;
}

/* 联系信息卡片 */
.contactCard {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.3s ease;
}

.contactCard:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.emailCard {
  background: rgba(24, 144, 255, 0.05);
  border-color: rgba(24, 144, 255, 0.1);
}

.phoneCard {
  background: rgba(82, 196, 26, 0.05);
  border-color: rgba(82, 196, 26, 0.1);
}

/* 附加信息区域 */
.additionalInfo {
  margin-top: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.additionalInfo:hover {
  background: #f5f5f5;
  border-color: #e0e0e0;
}

/* 统计卡片 */
.statsCard {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  cursor: pointer;
}

.statsCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contentArea {
    padding: 16px;
  }
  
  .titleBar {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .avatar {
    font-size: 24px;
  }
  
  .contactCard {
    padding: 6px 10px;
  }
  
  .additionalInfo {
    margin-top: 16px;
    padding: 12px;
  }
}

@media (max-width: 576px) {
  .personalInfoCard {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .contentArea {
    padding: 12px;
    border-radius: 10px;
  }
  
  .decorativeCircle1 {
    width: 80px;
    height: 80px;
    top: -40px;
    right: -40px;
  }
  
  .decorativeCircle2 {
    width: 60px;
    height: 60px;
    bottom: -30px;
    left: -30px;
  }
}

/* 加载动画 */
.loadingContainer {
  animation: fadeInUp 0.6s ease-out;
}

/* 统计卡片点击效果 */
.statsCard:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

/* 联系信息卡片点击效果 */
.contactCard:active {
  transform: translateX(2px) scale(0.98);
  transition: all 0.1s ease;
}

/* 设置按钮点击效果 */
.settingsButton:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}

/* 头像点击效果 */
.avatar:active {
  transform: scale(1.02);
  transition: all 0.1s ease;
}

/* 数据加载骨架屏效果 */
.skeletonCard {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 12px;
  height: 80px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 成功状态动画 */
.successAnimation {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* 错误状态动画 */
.errorAnimation {
  animation: errorShake 0.6s ease-out;
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* 渐入动画延迟 */
.fadeInDelay1 {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.fadeInDelay2 {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.fadeInDelay3 {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.fadeInDelay4 {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* 数据统计区域标题 */
.statsTitle {
  margin: 0 0 16px 0;
  color: #595959;
  font-weight: 600;
}

/* 统计卡片特定颜色 */
.vehicleCard {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.personnelCard {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
}

.warningCard {
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.15);
}

.alertCard {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.15);
}
